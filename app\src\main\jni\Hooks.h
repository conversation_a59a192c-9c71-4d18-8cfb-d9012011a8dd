
MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CLoginSystem"), OBFUSCATE("_LoginSuccess"), 1),"FF4303D1").Modify();   
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillControlIndicator") , OBFUSCATE("GetUseSkillDirection"), 1), (void *) GetUseSkillDirection, (void **) &_GetUseSkillDirection);

 Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CSkillButtonManager") , OBFUSCATE("UpdateLogic"), 1), (void *) UpdateLogic, (void **) &_UpdateLogic);
// Lấy các con trỏ hàm GetHero_Icon
GetHero_Icon = (String* (*)(void *, bool))IL2Cpp::Il2CppGetMethodOffset("Project_d.dll", "Assets.Scripts.GameSystem", "KillNotifyUT", "GetHero_Icon", 2);
GetHeroName = (String *(*)(int)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CHeroInfo"), OBFUSCATE("GetHeroName"), 1);

// Hook các hàm Update và Destroy
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("Update"), 0), (void *) ActorLinker_Update, (void **) &old_ActorLinker_Update);
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot"), OBFUSCATE("UpdateLogic"), 1), (void *) LActorRoot_UpdateLogic, (void **) &old_LActorRoot_UpdateLogic);
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("DestroyActor"), 0), (void *) ActorLinker_ActorDestroy, (void **) &old_ActorLinker_ActorDestroy);
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot"), OBFUSCATE("DestroyActor"), 1), (void *) LActorRoot_ActorDestroy, (void **) &old_LActorRoot_ActorDestroy);

// Hook các hàm Camera
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem"), OBFUSCATE("LateUpdate"), 0), (void *) _LateUpdate, (void **) &LateUpdate);
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem"), OBFUSCATE("GetCameraHeightRateValue"), 1), (void *) _GetCameraHeightRateValue, (void **) &GetCameraHeightRateValue);

// Lấy các con trỏ hàm ActorLinker
ActorLinker_IsHostPlayer = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("IsHostPlayer"), 0);
ActorLinker_IsHostCamp = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("IsHostCamp"), 0);
ActorLinker_ActorTypeDef = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_objType"), 0);
ActorLinker_COM_PLAYERCAMP = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_objCamp"), 0);
ActorLinker_getPosition = (Vector3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_position"), 0);
ActorLinker_get_HPBarVisible = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_HPBarVisible"), 0);
ActorLinker_get_ObjID = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_ObjID"), 0);
ActorLinker_get_bVisible = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("get_bVisible"), 0);
AsHero = (uintptr_t(*)(...)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker"), OBFUSCATE("AsHero"), 0);

// Lấy các con trỏ hàm LActorRoot
LActorRoot_get_forward = (VInt3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot"), OBFUSCATE("get_forward"), 0);
LActorRoot_get_location = (VInt3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot"), OBFUSCATE("get_location"), 0);
LActorRoot_LHeroWrapper = (uintptr_t (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot"), OBFUSCATE("AsHero"), 0);
LActorRoot_COM_PLAYERCAMP = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot"), OBFUSCATE("GiveMyEnemyCamp"), 0);
LActorRoot_get_bActive = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot"), OBFUSCATE("get_bActive"), 0);
LActorRoot_get_ObjID = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot"), OBFUSCATE("get_ObjID"), 0);

// Lấy các con trỏ hàm LObjWrapper
LObjWrapper_get_IsDeadState = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LObjWrapper"), OBFUSCATE("get_IsDeadState"), 0);
LObjWrapper_IsAutoAI = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LObjWrapper"), OBFUSCATE("IsAutoAI"), 0);

// Lấy các con trỏ hàm ValuePropertyComponent
ValuePropertyComponent_get_actorHp = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent"), OBFUSCATE("get_actorHp"), 0);
ValuePropertyComponent_get_actorHpTotal = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent"), OBFUSCATE("get_actorHpTotal"), 0);
    
    
