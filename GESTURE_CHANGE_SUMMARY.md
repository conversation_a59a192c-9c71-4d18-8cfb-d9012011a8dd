# 🖐️ Thay Đổi Gesture Mở Menu: 3 Ngón Tay → 4 Ngón Tay

## 📋 Tóm Tắt Thay Đổi

Đã thay đổi gesture để mở/đóng menu từ **3 ngón tay** thành **4 ngón tay** để tránh xung đột với các thao tác game thông thường.

## 🔧 Chi Tiết Thay Đổi

### **File: `app/src/main/jni/Main.cpp`**

#### **1. Thay Đổi Biến Global:**
```cpp
// CŨ:
static bool threeFingerDetected = false;
static std::chrono::steady_clock::time_point threeFingerStartTime;
static std::chrono::steady_clock::time_point lastThreeFingerTapTime;

// MỚI:
static bool fourFingerDetected = false;
static std::chrono::steady_clock::time_point fourFingerStartTime;
static std::chrono::steady_clock::time_point lastFourFingerTapTime;
```

#### **2. Thay Đổi Logic Detection:**
```cpp
// CŨ: Kiểm tra 3 ngón tay
if (touchCount == 3) {
    float currentPositions[3][2];
    for (int i = 0; i < 3; i++) {
        // Logic xử lý 3 ngón tay
    }
}

// MỚI: Kiểm tra 4 ngón tay
if (touchCount == 4) {
    float currentPositions[4][2];
    for (int i = 0; i < 4; i++) {
        // Logic xử lý 4 ngón tay
    }
}
```

#### **3. Cập Nhật Hướng Dẫn Sử Dụng:**
```cpp
// CŨ:
ImGui::TextWrapped("Để hiển thị/ẩn menu, đặt 3 ngón tay trên màn hình và giữ cố định trong 1.5 giây.");

// MỚI:
ImGui::TextWrapped("Để hiển thị/ẩn menu, đặt 4 ngón tay trên màn hình và tap 2 lần nhanh.");
```

## 🎮 Cách Sử Dụng Mới

### **Gesture Mở/Đóng Menu:**
1. **Đặt 4 ngón tay** lên màn hình cùng lúc
2. **Tap 2 lần nhanh** (double tap)
3. Menu sẽ hiện/ẩn

### **Thông Số Timing:**
- **Double Tap Interval**: 0.8 giây (thời gian tối đa giữa 2 lần tap)
- **Tap Duration**: 0.3 giây (thời gian tối đa cho mỗi lần tap)
- **Movement Threshold**: 15.0 pixel (ngưỡng di chuyển cho phép)

## 🔍 Lý Do Thay Đổi

### **Vấn Đề Với 3 Ngón Tay:**
- ❌ **Xung đột với game controls**: Nhiều game sử dụng 3 ngón tay cho các thao tác đặc biệt
- ❌ **Kích hoạt ngoài ý muốn**: Dễ bị trigger khi chơi game bình thường
- ❌ **Không tự nhiên**: 3 ngón tay không phải gesture phổ biến

### **Ưu Điểm Của 4 Ngón Tay:**
- ✅ **Ít xung đột**: Hiếm khi game sử dụng 4 ngón tay
- ✅ **Intentional gesture**: Phải cố ý mới có thể thực hiện
- ✅ **Dễ nhận biết**: Rõ ràng và khó nhầm lẫn
- ✅ **An toàn hơn**: Giảm nguy cơ kích hoạt ngoài ý muốn

## 🛠️ Technical Details

### **Touch Detection Logic:**
```cpp
// Kiểm tra số lượng ngón tay
int touchCount = TouchCount(nullptr);

if (touchCount == 4) {
    // Lưu vị trí của 4 ngón tay
    float currentPositions[4][2];
    bool areFingersSteady = true;
    
    // Kiểm tra từng ngón tay
    for (int i = 0; i < 4; i++) {
        UnityEngine_Touch_Fields touch = GetTouch(i);
        currentPositions[i][0] = touch.m_Position.fields.x;
        currentPositions[i][1] = touch.m_Position.fields.y;
        
        // Kiểm tra movement threshold
        if (movement > MOVEMENT_THRESHOLD) {
            areFingersSteady = false;
        }
    }
}
```

### **State Management:**
- **fourFingerDetected**: Đã phát hiện 4 ngón tay
- **isCurrentlyTapping**: Đang trong quá trình tap
- **tapCount**: Số lần tap hiện tại (0, 1, hoặc 2)
- **firstTouch**: Lần đầu tiên detect touch

### **Timing Control:**
- **TAP_DURATION**: 0.3s - Thời gian tối đa cho 1 lần tap
- **DOUBLE_TAP_INTERVAL**: 0.8s - Thời gian tối đa giữa 2 lần tap
- **MOVEMENT_THRESHOLD**: 15.0px - Ngưỡng di chuyển cho phép

## 🚀 Kết Quả

### **Trước Khi Thay Đổi:**
- 🔴 **3 ngón tay + giữ 1.5s** → Mở menu
- ⚠️ Dễ xung đột với game controls
- ⚠️ Kích hoạt ngoài ý muốn

### **Sau Khi Thay Đổi:**
- 🟢 **4 ngón tay + double tap** → Mở menu
- ✅ Ít xung đột với game
- ✅ Intentional gesture only
- ✅ Responsive và reliable

## 📝 Lưu Ý

1. **Cần 4 ngón tay chính xác**: Không hoạt động với 3 hoặc 5 ngón tay
2. **Double tap nhanh**: Phải tap 2 lần trong vòng 0.8 giây
3. **Giữ ngón tay ổn định**: Không di chuyển quá 15 pixel
4. **Gesture rõ ràng**: Phải cố ý thực hiện, không thể vô tình

## 🔧 Troubleshooting

**Nếu gesture không hoạt động:**
1. Đảm bảo đúng 4 ngón tay cùng lúc
2. Tap nhanh và rõ ràng (không giữ lâu)
3. Không di chuyển ngón tay khi tap
4. Kiểm tra Touch Sensitivity trong Settings tab

**Nếu kích hoạt ngoài ý muốn:**
1. Tăng Movement Threshold trong Settings
2. Giảm Double Tap Interval
3. Kiểm tra cách cầm thiết bị

Thay đổi này giúp cải thiện trải nghiệm người dùng và giảm thiểu xung đột với gameplay bình thường.
